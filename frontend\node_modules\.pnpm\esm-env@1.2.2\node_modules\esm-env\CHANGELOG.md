# esm-env

## 1.2.2

### Patch Changes

- fix: remove warning when env cannot be determined ([#15](https://github.com/benmccann/esm-env/pull/15))

## 1.2.1

### Patch Changes

- fix: address error in non-Vite bundlers when no conditions set ([#13](https://github.com/benmccann/esm-env/pull/13))

## 1.2.0

### Minor Changes

- feat: Vite 6 backwards compatibility with Vite 5 to continue to allow Vite to be run without specifying `--conditions=development` during development ([#8](https://github.com/benmccann/esm-env/pull/8))
