<a href="https://svelte.dev">
	<picture>
		<source media="(prefers-color-scheme: dark)" srcset="../../assets/banner_dark.png">
		<img src="../../assets/banner.png" alt="Svelte - web development for the rest of us" />
	</picture>
</a>

[![npm version](https://img.shields.io/npm/v/svelte.svg)](https://www.npmjs.com/package/svelte) [![license](https://img.shields.io/npm/l/svelte.svg)](LICENSE.md) [![Chat](https://img.shields.io/discord/457912077277855764?label=chat&logo=discord)](https://svelte.dev/chat)

## What is Svelte?

Svelte is a new way to build web applications. It's a compiler that takes your declarative components and converts them into efficient JavaScript that surgically updates the DOM.

Learn more at the [Svelte website](https://svelte.dev), or stop by the [Discord chatroom](https://svelte.dev/chat).

## Getting started

You can play around with <PERSON><PERSON><PERSON> in the [tutorial](https://svelte.dev/tutorial), [examples](https://svelte.dev/examples), and [REPL](https://svelte.dev/repl).

When you're ready to build a full-fledge application, we recommend using [SvelteKit](https://svelte.dev/docs/kit):

```sh
npx sv create my-app
cd my-app
npm install
npm run dev
```

See [the SvelteKit documentation](https://svelte.dev/docs/kit) to learn more.

## Changelog

[The Changelog for this package is available on GitHub](https://github.com/sveltejs/svelte/blob/master/packages/svelte/CHANGELOG.md).

## Supporting Svelte

Svelte is an MIT-licensed open source project with its ongoing development made possible entirely by fantastic volunteers. If you'd like to support their efforts, please consider:

- [Becoming a backer on Open Collective](https://opencollective.com/svelte).

Funds donated via Open Collective will be used for compensating expenses related to Svelte's development.
