{"name": "vitefu", "description": "Utilities for building frameworks with Vite", "version": "1.1.1", "license": "MIT", "type": "module", "types": "./src/index.d.ts", "exports": {".": {"import": "./src/index.js", "require": "./src/index.cjs"}}, "files": ["src"], "repository": {"type": "git", "url": "git+https://github.com/svitejs/vitefu.git"}, "bugs": {"url": "https://github.com/svitejs/vitefu/issues"}, "keywords": ["vite", "framework", "utilities"], "scripts": {"test": "uvu tests \".*\\.test\\.js\""}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0"}, "peerDependenciesMeta": {"vite": {"optional": true}}, "devDependencies": {"@types/node": "^14.18.63", "@types/pnpapi": "^0.0.5", "uvu": "^0.5.6", "vite": "^3.2.11"}, "workspaces": ["tests/deps/*", "tests/projects/*", "tests/projects/workspace/packages/*"]}