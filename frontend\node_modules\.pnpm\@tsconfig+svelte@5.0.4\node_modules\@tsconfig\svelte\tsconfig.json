{
  "$schema": "https://json.schemastore.org/tsconfig",
  "_version": "5.0.0",

  "compilerOptions": {
    "module": "esnext",
    "moduleResolution": "bundler",
    "target": "es2017",
    /**
      Svelte Preprocess cannot figure out whether you have a value or a type, so tell TypeScript
      to enforce using `import type` instead of `import` for Types.
     */
    "verbatimModuleSyntax": true,
    /**
      To have warnings/errors of the Svelte compiler at the correct position,
      enable source maps by default.
     */
    "sourceMap": true,

    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true
  }
}
